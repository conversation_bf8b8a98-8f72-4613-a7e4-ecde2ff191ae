package com.yxt.talent.rv.application.calimeet;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.rv.application.calimeet.dto.CaliIndexNumDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliLastRecordUserDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliUserIndexDTO;
import com.yxt.talent.rv.application.common.CommonAppService;
import com.yxt.talent.rv.application.meet.dto.CaliMeetDimDTO;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.common.enums.CaliResultTypeEnum;
import com.yxt.talent.rv.application.xpd.result.XpdResultCalcService;
import com.yxt.talent.rv.application.xpd.result.sorter.UserDimResultSorterContext;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetMoveCmd;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliResult4Query;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.*;
import com.yxt.talent.rv.controller.manage.meet.viewobj.MeetResultDetailVO;
import com.yxt.talent.rv.controller.manage.meet.viewobj.MeetUserDimResultVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultsVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.MathUtil;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetRecordMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetResultUserDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.service.remote.L10nAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.yxt.common.util.Validate.isNotNull;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_GRID_CELL_NOT_FOUND;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_GRID_NOT_FOUND;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_RULE_CONF_NOT_FOUND;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/21
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CaliGridService {
    private final XpdRuleConfMapper xpdRuleConfMapper;
    private final XpdGridCellMapper xpdGridCellMapper;
    private final XpdGridMapper xpdGridMapper;
    private final XpdGridRatioMapper xpdGridRatioMapper;
    private final CalimeetMapper calimeetMapper;
    private final CalimeetUserMapper calimeetUserMapper;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final XpdGridLevelMapper xpdGridLevelMapper;
    private final XpdResultCalcService xpdResultCalcService;
    private final XpdDimCombMapper xpdDimCombMapper;
    private final XpdMapper xpdMapper;
    private final L10nAclService l10nAclService;
    private final XpdDimMapper xpdDimMapper;
    private final CalimeetResultUserDimMapper calimeetResultUserDimMapper;
    private final CalimeetRecordMapper calimeetRecordMapper;
    private final SpsdAclService spsdAclService;
    private final UserDimResultSorterContext userDimResultSorterContext;
    private final AppProperties appProperties;
    private final CommonAppService commonAppService;

    public CaliGridViewVO caliGridView(String orgId, CaliResult4Query query) {
        CaliGridViewVO result = new CaliGridViewVO();

        String xpdId = query.getProjectId();
        XpdGridPO xpdGrid = selectXpdGrid(orgId, xpdId);
        String gridId = xpdGrid.getId();
        String dimCombId = query.getDimCombId();
        String caliMeetId = query.getMeetingId();
        // 处理部门人员
        // 查询部门下子部门id
        if (CollectionUtils.isNotEmpty(query.getScopeDeptIds())) {
            query.setScopeDeptIds(commonAppService.findChildDeptIds(orgId, query.getScopeDeptIds()));
        }

        // 是否开启比例控制
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(query.getMeetingId(), orgId);
        Validate.isNotNull(calimeet, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        result.setCalimeetType(calimeet.getCalimeetType());
        // 宫格内是否设置了占比
        if (calimeet.getShowRatio() == 0) {
            return result;
        }

        // Get grid cells based on config type
        List<XpdGridCellPO> gridCells = xpdGridCellMapper.selectByXpdIdAndGridIdAndDimCombId(
            orgId, xpdId, gridId, xpdGrid.getConfigType() == 1 ? dimCombId : "");
        Validate.isNotEmpty(gridCells, XPD_GRID_CELL_NOT_FOUND);
        Map<String, XpdGridCellPO> gridCellMap =
            StreamUtil.list2map(gridCells, XpdGridCellPO::getId);
        // 获取 宫格内人数

        List<XpdGridRatioPO> gridRatios =
            xpdGridRatioMapper.selectByXpdIdAndGridIdAndDimCombId(
                orgId, xpdId, gridId, xpdGrid.getConfigType() == 1 ? dimCombId : "");
        if (CollectionUtils.isEmpty(gridRatios)) {
            return result;
        }

        List<CaliIndexNumDTO> caliCellIndexList = calimeetUserMapper.findCaliCellIndexMsg(orgId, caliMeetId, query);

        // 总人数
        int allUserCount = calimeetUserMapper.countByOrgIdAndCalimeetId(orgId, caliMeetId, query);
        Map<Integer, Integer> cellUseMap =
            StreamUtil.list2map(caliCellIndexList, CaliIndexNumDTO::getCellIndex, CaliIndexNumDTO::getCellCount);
        List<CaliGridViewDataVO> resList = new ArrayList<>();
        for (XpdGridRatioPO gridRatio : gridRatios) {
            CaliGridViewDataVO caliGridView = new CaliGridViewDataVO();
            String gridCellIds = gridRatio.getGridCellIds();
            String[] cellIds = gridCellIds.split(";");
            List<String> cellNames = new ArrayList<>();
            int cellNum = 0;
            for (String cellId : cellIds) {
                XpdGridCellPO xpdGridCell = gridCellMap.get(cellId);
                if (xpdGridCell != null) {
                    cellNames.add(xpdGridCell.getCellName());
                    Integer indexCount = cellUseMap.getOrDefault(xpdGridCell.getCellIndex(), 0);

                    cellNum = +indexCount;
                }
            }
            caliGridView.setCellName(String.join("\\", cellNames));
            caliGridView.setRatio(gridRatio.getRatio());
            caliGridView.setActualRatio(MathUtil.dividePer(cellNum, allUserCount, 2));
            caliGridView.setOrderIndex(gridRatio.getOrderIndex());
            resList.add(caliGridView);
        }

        result.setGridViewDataVOList(resList);
        return result;
    }

    public XpdGridPO selectXpdGrid(String orgId, String xpdId) {
        XpdRuleConfPO xpdRuleConf = xpdRuleConfMapper.selectByXpdId(orgId, xpdId);
        isNotNull(xpdRuleConf, XPD_RULE_CONF_NOT_FOUND);
        String gridId = xpdRuleConf.getGridId();
        XpdGridPO xpdGrid = xpdGridMapper.selectByPrimaryKey(gridId);
        isNotNull(xpdGrid, XPD_GRID_NOT_FOUND);
        return xpdGrid;
    }

    public MeetResultDetailVO findGridUser(String orgId, CaliResult4Query query) {
        MeetResultDetailVO res = new MeetResultDetailVO();
        if (CollectionUtils.isNotEmpty(query.getScopeDeptIds())) {
            query.setScopeDeptIds(commonAppService.findChildDeptIds(orgId, query.getScopeDeptIds()));
        }

        // 1. 获取校准会议信息
        CalimeetPO calimeet = getCalimeetInfo(orgId, query.getMeetingId());
        res.setCalimeetType(calimeet.getCalimeetType());

        // 2. 获取维度组合信息
        XpdDimCombPO xpdDimComb = getDimComb(query.getDimCombId());
        String xSdDimId = xpdDimComb.getXSdDimId();
        String ySdDimId = xpdDimComb.getYSdDimId();

        // 3. 获取用户索引和网格信息
        List<CaliUserIndexDTO> userIndexList = getUserIndexList(orgId, query);
        int allUserCount = calimeetUserMapper.countByOrgIdAndCalimeetId(orgId, query.getMeetingId(), query);
        
        // 4. 获取网格配置
        Map<Integer, XpdGridCellPO> gridCellMap = getGridCellMap(orgId, query.getProjectId(), query.getDimCombId());
        
        // 5. 获取用户信息和维度结果
        List<String> userIds = userIndexList.stream().map(CaliUserIndexDTO::getUserId).toList();
        Map<String, UdpLiteUserPO> userMap = getUserMap(orgId, userIds);
        Map<String, List<CalimeetResultUserDimPO>> userDimMap = getUserDimensionResults(orgId, calimeet.getId(), userIds);
        
        // 6. 计算每个宫格的人数
        Map<Integer, Long> cellUserMap = userIndexList.stream()
            .collect(Collectors.groupingBy(CaliUserIndexDTO::getCellIndex, Collectors.counting()));

        // 7. 构建用户结果列表，按宫格分组
        Map<Integer, List<XpdUserDimResultsVO>> cellUserResultsMap = buildCellUserResultsMap(userIndexList, userMap, userDimMap);

        // 8. 设置默认排序维度
        String sortDimId = StringUtils.isBlank(query.getSortDimId()) ? xSdDimId : query.getSortDimId();

        // 9. 对每个宫格内的用户进行排序并转换为最终结果
        List<XpdUserGridResultVO> calibrationList = sortAndConvertResults(
            userIndexList, cellUserResultsMap, sortDimId, xSdDimId, ySdDimId, 
            orgId, query.getProjectId(), gridCellMap, query, allUserCount, cellUserMap);

        res.setCalibrationList(calibrationList);
        return res;
    }
    
    /**
     * 获取校准会议信息
     */
    private CalimeetPO getCalimeetInfo(String orgId, String meetingId) {
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(meetingId, orgId);
        Validate.isNotNull(calimeet, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        return calimeet;
    }
    
    /**
     * 获取维度组合信息
     */
    private XpdDimCombPO getDimComb(String dimCombId) {
        XpdDimCombPO xpdDimComb = xpdDimCombMapper.selectByPrimaryKey(dimCombId);
        isNotNull(xpdDimComb, ExceptionKeys.XPD_DIM_COMB_NOT_FOUND);
        return xpdDimComb;
    }
    
    /**
     * 获取用户索引列表
     */
    private List<CaliUserIndexDTO> getUserIndexList(String orgId, CaliResult4Query query) {
        List<CaliUserIndexDTO> userIndexList = calimeetUserMapper.findUserIndex(orgId, query.getMeetingId(), query);
        return userIndexList.stream()
            .sorted(Comparator.comparing(CaliUserIndexDTO::getCellIndex))
            .toList();
    }
    
    /**
     * 获取网格单元格映射
     */
    private Map<Integer, XpdGridCellPO> getGridCellMap(String orgId, String xpdId, String dimCombId) {
        XpdGridPO xpdGrid = selectXpdGrid(orgId, xpdId);
        List<XpdGridCellPO> gridCells = xpdGridCellMapper.selectByXpdIdAndGridIdAndDimCombId(
            orgId, xpdId, xpdGrid.getId(), xpdGrid.getConfigType() == 1 ? dimCombId : "");
        return StreamUtil.list2map(gridCells, XpdGridCellPO::getCellIndex);
    }
    
    /**
     * 获取用户信息映射
     */
    private Map<String, UdpLiteUserPO> getUserMap(String orgId, List<String> userIds) {
        List<UdpLiteUserPO> udpLiteUsers = udpLiteUserMapper.selectByUserIdsIncludeDelete(orgId, userIds);
        return StreamUtil.list2map(udpLiteUsers, UdpLiteUserPO::getId);
    }
    
    /**
     * 获取用户维度结果映射
     */
    private Map<String, List<CalimeetResultUserDimPO>> getUserDimensionResults(String orgId, String calimeetId, List<String> userIds) {
        List<CalimeetResultUserDimPO> resultUserDims = 
            calimeetResultUserDimMapper.selectByCaliMeetIdAndUserIds(orgId, calimeetId, userIds);
        return resultUserDims.stream().collect(Collectors.groupingBy(CalimeetResultUserDimPO::getUserId));
    }
    
    /**
     * 构建单元格用户结果映射
     */
    private Map<Integer, List<XpdUserDimResultsVO>> buildCellUserResultsMap(
            List<CaliUserIndexDTO> userIndexList, 
            Map<String, UdpLiteUserPO> userMap, 
            Map<String, List<CalimeetResultUserDimPO>> userDimMap) {
        
        Map<Integer, List<XpdUserDimResultsVO>> cellUserResultsMap = new HashMap<>();
        
        for (CaliUserIndexDTO caliUserIndex : userIndexList) {
            String userId = caliUserIndex.getUserId();
            UdpLiteUserPO udpLiteUser = userMap.get(userId);
            if (udpLiteUser == null) {
                log.warn("LOG21293:用户不存在 {}", userId);
                continue;
            }

            XpdUserDimResultsVO userResult = new XpdUserDimResultsVO();
            userResult.setUserId(userId);
            userResult.setFullname(udpLiteUser.getFullname());
            userResult.setUsername(udpLiteUser.getUsername());
            userResult.setImgUrl(udpLiteUser.getImgUrl());

            // 填充用户维度结果
            List<CalimeetResultUserDimPO> userDims = userDimMap.get(userId);
            if (CollectionUtils.isNotEmpty(userDims)) {
                List<XpdUserDimResultVO> dimResults = userDims.stream()
                    .map(this::convertToXpdUserDimResultVO)
                    .collect(Collectors.toList());
                userResult.setUserDimResults(dimResults);
            }

            Integer cellIndex = caliUserIndex.getCellIndex();
            cellUserResultsMap.computeIfAbsent(cellIndex, k -> new ArrayList<>()).add(userResult);
        }
        
        return cellUserResultsMap;
    }
    
    /**
     * 对每个宫格内的用户进行排序并转换为最终结果
     */
    private List<XpdUserGridResultVO> sortAndConvertResults(
            List<CaliUserIndexDTO> userIndexList, 
            Map<Integer, List<XpdUserDimResultsVO>> cellUserResultsMap,
            String sortDimId, String xSdDimId, String ySdDimId,
            String orgId, String xpdId,
            Map<Integer, XpdGridCellPO> gridCellMap, 
            CaliResult4Query query,
            int allUserCount,
            Map<Integer, Long> cellUserMap) {
        
        List<XpdUserGridResultVO> calibrationList = new ArrayList<>();
        Map<Integer, Boolean> processedCells = new HashMap<>();

        for (CaliUserIndexDTO caliUserIndex : userIndexList) {
            Integer cellIndex = caliUserIndex.getCellIndex();

            // 如果这个宫格已经处理过，跳过
            if (processedCells.containsKey(cellIndex)) {
                continue;
            }

            List<XpdUserDimResultsVO> cellUsers = cellUserResultsMap.get(cellIndex);
            if (cellUsers == null || cellUsers.isEmpty()) {
                continue;
            }

            // 使用排序上下文进行排序
            List<XpdUserDimResultsVO> sortedUserDimResults = userDimResultSorterContext.sort(
                cellUsers, sortDimId, xSdDimId, ySdDimId, orgId, xpdId);

            // 转换为XpdUserDimResultsVO
            for (XpdUserDimResultsVO sortedUserDimResult : sortedUserDimResults) {
                // 找到对应的CaliUserIndexDTO
                CaliUserIndexDTO matchingIndex = findMatchingUserIndex(userIndexList, sortedUserDimResult.getUserId(), cellIndex);
                if (matchingIndex != null) {
                    XpdUserGridResultVO finalResult = convertToXpdUserGridResultVO(
                        sortedUserDimResult, matchingIndex, gridCellMap, query, allUserCount, cellUserMap);
                    
                    // 注释掉的第三维度处理代码保留原样
                    // if (StringUtils.isNotBlank(query.getThirdDimId())) {
                    //     CaliThirdDimResultVO thirdDimResult = buildThirdDimResult(
                    //         sortedUserDimResult, query.getThirdDimId(), orgId, xpdId);
                    //     finalResult.setThirdDimResult(thirdDimResult);
                    // }

                    calibrationList.add(finalResult);
                }
            }

            // 标记这个宫格已经处理过
            processedCells.put(cellIndex, true);
        }
        
        return calibrationList;
    }
    
    /**
     * 查找匹配的用户索引
     */
    private CaliUserIndexDTO findMatchingUserIndex(
            List<CaliUserIndexDTO> userIndexList, 
            String userId, 
            Integer cellIndex) {
        return userIndexList.stream()
            .filter(idx -> idx.getUserId().equals(userId) && idx.getCellIndex().equals(cellIndex))
            .findFirst()
            .orElse(null);
    }

    public PagingList<CaliCellUserVO> findGridUserPage(String orgId, CaliResult4Query query) {
        PageRequest pageRequest = ApiUtil.getPageRequest(ApiUtil.getRequestByContext());
        IPage<CaliCellUserVO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<CaliCellUserVO> gridUserPage = calimeetUserMapper.findGridUserPage(page, orgId, query);
        return BeanCopierUtil.toPagingList(gridUserPage);

    }

    public CaliDimResultResp moveView(String orgId, CaliMeetMoveCmd cmd) {
        String calimeetId = cmd.getCalimeetId();
        String dimCombId = cmd.getDimCombId();

        XpdDimCombPO xpdDimComb = xpdDimCombMapper.selectByPrimaryKey(dimCombId);

        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(calimeetId, orgId);
        String xpdId = calimeet.getXpdId();
        XpdPO xpd = xpdMapper.selectById(xpdId);
        XpdGridPO xpdGrid = xpdGridMapper.selectByXpdId(orgId, xpdId);

        List<XpdGridCellPO> xpdGridCells = xpdGridCellMapper.listByGridIdAndDimCombId(orgId, xpdGrid.getId(),
            xpdGrid.getConfigType() == 0 ? "" : dimCombId);
        Map<Integer, XpdGridCellPO> gridCellMap =
            StreamUtil.list2map(xpdGridCells, XpdGridCellPO::getCellIndex);

        // 维度分层
        List<XpdGridLevelPO> gridLevels = xpdGridLevelMapper.listByXpdId(orgId, xpdId);
        Map<Integer, String> levelMap =
            StreamUtil.list2map(gridLevels, XpdGridLevelPO::getOrderIndex, XpdGridLevelPO::getId);
        XpdGridCellPO xpdGridCell = gridCellMap.get(cmd.getCellIndex());

        List<CaliUpdateUserResultReq> resultList = new ArrayList<>();
        CaliUpdateUserResultReq xLevel = new CaliUpdateUserResultReq();
        xLevel.setSdDimId(xpdDimComb.getXSdDimId());
        Integer xIndex = xpdGridCell.getXIndex();
        xLevel.setCaliVal(levelMap.get(xIndex));
        xLevel.setGridLevelId(levelMap.get(xIndex));
        resultList.add(xLevel);

        // y维度
        CaliUpdateUserResultReq yLevel = new CaliUpdateUserResultReq();
        yLevel.setSdDimId(xpdDimComb.getYSdDimId());
        Integer yIndex = xpdGridCell.getYIndex();
        yLevel.setCaliVal(levelMap.get(yIndex));
        yLevel.setGridLevelId(levelMap.get(yIndex));
        resultList.add(yLevel);

        xpdResultCalcService.preHandleCaliResult(CaliResultTypeEnum.TYPE_DIM_LEVEL.getType(), resultList);

        CalimeetDimResultDto resultDto = calimeetRecordMapper.getLastDetail(orgId, calimeetId, cmd.getUserId());
        CaliUpdateUserResultWrapDto caliUpdateUserResultWrapDto =
            CommonUtils.tryParseObject(resultDto.getCaliDetails(), CaliUpdateUserResultWrapDto.class);
        List<CaliUpdateUserResultDto> userResults = caliUpdateUserResultWrapDto.getUserResults();
        List<String> dimIds = resultList.stream().map(CaliUpdateUserResultReq::getSdDimId).toList();
        for (CaliUpdateUserResultDto userResult : userResults) {
            if (dimIds.contains(userResult.getSdDimId())) {
                continue;
            }
            CaliUpdateUserResultReq userResultReq = new CaliUpdateUserResultReq();
            userResultReq.setSdDimId(userResult.getSdDimId());
            userResultReq.setGridLevelId(userResult.getGridLevelId());
            resultList.add(userResultReq);
        }


        return xpdResultCalcService.viewCalcCaliResult(orgId, calimeetId, cmd.getUserId(), resultList, Pair.of(true, resultDto));
    }


    public MeetUserDimResultVO getUserResult(
        String orgId, String projectId, String userId, String meetingId, String lang) {
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(meetingId, orgId);
        Validate.isNotNull(calimeet, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        MeetUserDimResultVO result = new MeetUserDimResultVO();
        UdpLiteUserPO udpUser = udpLiteUserMapper.selectById(userId);
        Validate.isNotNull(udpUser, ExceptionKeys.USER_NOT_EXISTED);
        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        l10nAclService.translateList(enableLocalization, List.of(orgId), lang, UdpLiteUserPO.class, List.of(udpUser));

        result.setUserId(userId);
        result.setDeptName(udpUser.getDeptName());
        result.setImgUrl(udpUser.getImgUrl());
        result.setPosition(udpUser.getPositionName());

        List<XpdDimPO> dimList = xpdDimMapper.listByXpdId(orgId, projectId);
        if (CollectionUtils.isEmpty(dimList)) {
            return result;
        }
        // 校准前
        List<CalimeetResultUserDimPO> resultUserDims =
            calimeetResultUserDimMapper.selectByCaliMeetIdAndUserIds(orgId, calimeet.getId(),
                Lists.newArrayList(userId));
        Map<String, String> beforeDimMap = StreamUtil.list2map(resultUserDims, CalimeetResultUserDimPO::getSdDimId,
            CalimeetResultUserDimPO::getGridLevelId);

        // 校准后
        CaliLastRecordUserDTO lastRecord =
            calimeetUserMapper.findLastRecord(orgId, calimeet.getId(), userId);

        Map<String, String> afterMap = new HashMap<>();
        if (lastRecord != null) {
            String resultDetails = lastRecord.getResultDetails();
            if (StringUtils.isNotEmpty(resultDetails)) {
                CaliDimResultWrapDto
                    dimResults = CommonUtils.tryParseObject(resultDetails, CaliDimResultWrapDto.class);
                List<CaliDimResultDto> dimUserResults = dimResults.getDimResults();
                if (CollectionUtils.isNotEmpty(dimUserResults)) {
                    afterMap = StreamUtil.list2map(dimUserResults, CaliDimResultDto::getSdDimId,
                        CaliDimResultDto::getGridLevelId);
                }
            }
        }

        // 维度分层
        List<XpdGridLevelPO> gridLevels = xpdGridLevelMapper.listByXpdId(orgId, calimeet.getXpdId());
        Map<String, XpdGridLevelPO> levelMap =
            StreamUtil.list2map(gridLevels, XpdGridLevelPO::getId);

        // 维度名称
        List<String> dimIds = dimList.stream().map(XpdDimPO::getSdDimId).toList();
        if (CollectionUtils.isEmpty(dimIds)) {
            return result;
        }
        List<DimensionList4Get> baseDimDetails = spsdAclService.getBaseDimDetail(orgId, dimIds);
        Map<String, String> dimNameMap =
            StreamUtil.list2map(baseDimDetails, DimensionList4Get::getId, DimensionList4Get::getDmName);

        List<CaliMeetDimDTO> caliMeetDimList = new ArrayList<>();
        for (XpdDimPO xpdDim : dimList) {
            CaliMeetDimDTO dto = new CaliMeetDimDTO();

            String sdDimId = xpdDim.getSdDimId();
            dto.setDimId(sdDimId);
            dto.setDimensionName(dimNameMap.getOrDefault(sdDimId, ""));
            // 校准前
            String beforelevelId = beforeDimMap.get(sdDimId);
            if (beforelevelId != null) {
                XpdGridLevelPO xpdGridLevel = levelMap.get(beforelevelId);
                if (xpdGridLevel != null) {
                    dto.setInitLevel(xpdGridLevel.getOrderIndex());
                    dto.setInitLevelName(xpdGridLevel.getLevelName());
                    dto.setInitDimColor(xpdGridLevel.getThirdDimColor());
                }


            }
            // 校准后
            String afterLevelId = afterMap.get(sdDimId);
            if (afterLevelId != null) {
                XpdGridLevelPO xpdGridLevel = levelMap.get(afterLevelId);
                if (xpdGridLevel != null) {
                    dto.setLastLevel(xpdGridLevel.getOrderIndex());
                    dto.setLastLevelName(xpdGridLevel.getLevelName());
                    dto.setLastDimColor(xpdGridLevel.getThirdDimColor());
                } else {
                    dto.setLastLevel(dto.getInitLevel());
                    dto.setLastLevelName(dto.getInitLevelName());
                    dto.setLastDimColor(dto.getInitDimColor());
                }

            }
            dto.setDimensionType(xpdDim.getDimType());
            caliMeetDimList.add(dto);
        }
        result.setResult(caliMeetDimList);
        return result;
    }

    /**
     * 转换 CalimeetResultUserDimPO 到 XpdUserDimResultVO
     */
    private XpdUserDimResultVO convertToXpdUserDimResultVO(CalimeetResultUserDimPO userDim) {
        XpdUserDimResultVO result = new XpdUserDimResultVO();
        result.setSdDimId(userDim.getSdDimId());
        result.setGridLevelId(userDim.getGridLevelId());
        result.setScoreValue(userDim.getScoreValue());
        result.setQualifiedPtg(userDim.getQualifiedPtg());
        result.setPerfResultId(userDim.getPerfResultId());
        return result;
    }

    /**
     * 转换 XpdUserDimResultsVO 到 PrjUserResultVO
     */
    private XpdUserGridResultVO convertToXpdUserGridResultVO(XpdUserDimResultsVO sortedUserDimResult,
                                                     CaliUserIndexDTO caliUserIndex,
                                                     Map<Integer, XpdGridCellPO> gridCellMap,
                                                     CaliResult4Query query,
                                                     int allUserCount,
                                                     Map<Integer, Long> cellUserMap) {
        XpdUserGridResultVO result = new XpdUserGridResultVO();
        result.setUserId(sortedUserDimResult.getUserId());
        result.setFullName(sortedUserDimResult.getFullname());
        result.setUserName(sortedUserDimResult.getUsername());
        result.setImgUrl(sortedUserDimResult.getImgUrl());
        result.setUserDimResults(sortedUserDimResult.getUserDimResults());
        result.setXAxis(query.getAxisX());
        result.setYAixs(query.getAxisY());

        // 校准前维度等级
        XpdGridCellPO xpdGridCell = gridCellMap.get(caliUserIndex.getOriginalCellIndex());
        if (xpdGridCell != null) {
            result.setXValue(xpdGridCell.getXIndex());
            result.setYValue(xpdGridCell.getYIndex());
        } else {
            // 如果找不到原始单元格，设置默认值或记录日志
            log.info("LOG21313:找不到原始单元格索引: {}, userId: {}", caliUserIndex.getOriginalCellIndex(), sortedUserDimResult.getUserId());
            result.setXValue(0);
            result.setYValue(0);
        }

        // 校准后
        XpdGridCellPO afterGridCell = gridCellMap.get(caliUserIndex.getCellIndex());
        if (afterGridCell != null) {
            result.setXCheckValue(afterGridCell.getXIndex());
            result.setYCheckValue(afterGridCell.getYIndex());
        } else {
            // 如果找不到校准后单元格，设置默认值或记录日志
            log.info("LOG21323:找不到校准后单元格索引: {}, userId: {}", caliUserIndex.getCellIndex(), sortedUserDimResult.getUserId());
            result.setXCheckValue(0);
            result.setYCheckValue(0);
        }

        result.setTotalUser((long) allUserCount);
        Long userNum = cellUserMap.get(caliUserIndex.getCellIndex());
        result.setPercentStr(MathUtil.dividePerStr(userNum, allUserCount, 2));

        // 计算距离
        if (xpdGridCell != null && afterGridCell != null) {
            result.setCaliShift(MathUtil.calculateShortestDistance(
                xpdGridCell.getXIndex(), xpdGridCell.getYIndex(),
                afterGridCell.getXIndex(), afterGridCell.getYIndex()));
        } else {
            result.setCaliShift(0);
        }

        return result;
    }

    /**
     * 构建第三维度结果
     */
    private CaliThirdDimResultVO buildThirdDimResult(XpdUserDimResultsVO userResult,
                                                     String thirdDimId,
                                                     String orgId,
                                                     String xpdId) {
        if (CollectionUtils.isEmpty(userResult.getUserDimResults())) {
            return null;
        }

        // 查找第三维度的结果
        XpdUserDimResultVO thirdDimResult = userResult.getUserDimResults().stream()
            .filter(dim -> thirdDimId.equals(dim.getSdDimId()))
            .findFirst()
            .orElse(null);

        if (thirdDimResult == null) {
            return null;
        }

        CaliThirdDimResultVO result = new CaliThirdDimResultVO();
        result.setDimId(thirdDimId);
        result.setGridLevelId(thirdDimResult.getGridLevelId());

        // 获取维度名称
        List<DimensionList4Get> baseDimDetails = spsdAclService.getBaseDimDetail(orgId, List.of(thirdDimId));
        if (CollectionUtils.isNotEmpty(baseDimDetails)) {
            result.setDimName(baseDimDetails.get(0).getDmName());
        }

        // 获取分层信息和颜色
        if (StringUtils.isNotBlank(thirdDimResult.getGridLevelId())) {
            XpdGridLevelPO gridLevel = xpdGridLevelMapper.selectByPrimaryKey(thirdDimResult.getGridLevelId());
            if (gridLevel != null) {
                result.setGridLevelName(gridLevel.getLevelName());
                result.setOrderIndex(gridLevel.getOrderIndex());

                // 获取第三维度颜色
                XpdGridPO xpdGrid = selectXpdGrid(orgId, xpdId);
                result.setThirdDimColor(gridLevel.decideThirdDimColor(xpdGrid, isForce, appProperties));
            }
        }

        return result;
    }

}
